{"name": "claude-talk-to-figma-mcp", "description": "<PERSON> to Figma MCP", "version": "0.5.2", "module": "dist/talk_to_figma_mcp/server.js", "main": "dist/talk_to_figma_mcp/server.js", "bin": {"claude-talk-to-figma-mcp": "dist/talk_to_figma_mcp/server.js", "claude-talk-to-figma-mcp-socket": "dist/socket.js"}, "files": ["dist", "readme.md", "LICENSE", "TESTING.md", "CHANGELOG.md"], "type": "module", "scripts": {"start": "bun run dist/talk_to_figma_mcp/server.js", "socket": "bun run dist/socket.js", "setup": "./scripts/setup.sh", "build": "tsup && chmod +x dist/talk_to_figma_mcp/server.js dist/socket.js", "build:watch": "tsup --watch", "dev": "bun run build:watch", "pub:release": "bun run build && npm publish", "configure-claude": "node scripts/configure-claude.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "node scripts/test-integration.js"}, "devDependencies": {"@types/bun": "latest", "@types/jest": "^29.5.12", "bun-types": "^1.2.9", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "latest", "uuid": "latest", "ws": "latest", "zod": "latest"}, "keywords": ["claude", "figma", "mcp", "plugin", "ai", "design", "automation"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/arinspunk/claude-talk-to-figma-mcp.git"}, "bugs": {"url": "https://github.com/arinspunk/claude-talk-to-figma-mcp/issues"}, "homepage": "https://github.com/arinspunk/claude-talk-to-figma-mcp#readme"}